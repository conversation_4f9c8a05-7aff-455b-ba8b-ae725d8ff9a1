<template>
  <div class="flex min-h-screen flex-col">
    <!-- Page Heading -->

    <!-- Page Content -->
    <main class="flex flex-1 items-stretch gap-48 xl:gap-80">
      <div
        class="relative hidden w-1/2 bg-surface-information-light lg:flex lg:items-end lg:justify-end"
      >
        <AuthVectorImage />
      </div>

      <div
        class="mx-auto my-48 flex w-full max-w-[475px] flex-col items-center px-24 lg:mx-0 lg:my-104 lg:pl-0"
      >
        <Logo />
        <slot />
      </div>
    </main>
    <CookieConsent />
  </div>
</template>

<script setup>
import Footer from "@/Components/Footer/Footer.vue";
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";

import Logo from "./Logo.vue";
import AuthVectorImage from "./AuthVectorImage.vue";

defineProps({
  title: String,
});

const footerLinks = [
  { text: "Privacy policy", url: "/privacy-policy" },
  { text: "Terms and conditions", url: "/terms-of-service" },
];
</script>
