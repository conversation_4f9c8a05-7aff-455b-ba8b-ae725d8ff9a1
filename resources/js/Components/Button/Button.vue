<template>
    <button type="button" class="btn" :class="['disabled:opacity-50 disabled:cursor-not-allowed',
        { 'btn--transparent': transparent },
        { 'rounded-3xl': rounded },
        sizeClass,
        colorClass,
    ]" :disabled="disabled">
        <slot></slot>
    </button>
</template>

<script setup>
import { computed } from "vue";
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
    transparent: {
        type: Boolean,
        default: false,
    },
    rounded: {
        type: Boolean,
        default: false,
    },
    size: {
        type: String,
        default: "md",
        validator: (value) => {
            return ["md", "lg", "xl"].includes(value);
        },
    },
    color: {
        type: String,
        default: "default",
        validator: (value) => {
            return [
                "default",
                "black",
                "informationPlus",
                "information",
                "success",
                "warning",
                "error",
                "action",
            ].includes(value);
        },
    },
});

const sizeClass = computed(() => {
    return {
        "btn--md": props.size === "md",
        "btn--lg": props.size === "lg",
        "btn--xl": props.size === "xl",
    };
});

const colorClass = computed(() => {
    return {
        "btn--default": props.color === "default",
        "btn--black": props.color === "black",
        "btn--information-plus": props.color === "informationPlus",
        "btn--information": props.color === "information",
        "btn--success": props.color === "success",
        "btn--warning": props.color === "warning",
        "btn--error": props.color === "error",
        "btn--action": props.color === "action",
    };
});
</script>
