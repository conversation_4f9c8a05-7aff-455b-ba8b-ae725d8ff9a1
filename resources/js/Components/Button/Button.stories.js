import { fn } from "@storybook/test";
import Button from "./Button.vue";
import { color } from "storybook/internal/theming";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
    component: Button,
    tags: ["autodocs"],
    argTypes: {
        size: {
            control: { type: "select" },
            options: ["md", "lg", "xl"],
            default: "md",
        },
        color: {
            control: { type: "select" },
            options: [
                "default",
                "black",
                "informationPlus",
                "information",
                "success",
                "warning",
                "error",
                "action",
            ],
            default: "default",
        },
    },
    // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
    args: {
        label: "Button",
        disabled: false,
        transparent: false,
        rounded: false,
        onClick: fn(),
    },
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
    args: {
        color: "default",
    },
};

export const Black = {
    args: {
        color: "black",
    },
};
export const InformationPlus = {
    args: {
        color: "informationPlus",
    },
};

export const Information = {
    args: {
        color: "information",
    },
};

export const Success = {
    args: {
        color: "success",
    },
};

export const Warning = {
    args: {
        color: "warning",
    },
};

export const Error = {
    args: {
        color: "error",
    },
};

export const Action = {
    args: {
        color: "action",
    },
};

export const Rounded = {
    args: {
        color: "action",
        rounded: true,
    },
};

export const Disabled = {
    args: {
        color: "action",
        disabled: true,
    },
};

export const Large = {
    args: {
        color: "action",
        size: "lg",
    },
};

export const XLarge = {
    args: {
        color: "action",
        size: "xl",
    },
};

export const XLargeRounded = {
    args: {
        color: "action",
        size: "xl",
        rounded: true,
    },
};
